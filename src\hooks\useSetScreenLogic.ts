import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRIR } from '@/hooks/useRIR'
import { calculatePercentageChange, roundWeight } from '@/utils/weightUtils'
import type { RecommendationModel } from '@/types'

export function useSetScreenLogic(exerciseId?: number) {
  const router = useRouter()
  const { saveSet, isLoading, error, getRecommendation } = useWorkout()
  const {
    exercises,
    currentExerciseIndex,
    currentSetIndex,
    workoutSession,
    nextSet,
    setCurrentSetIndex,
    nextExercise,
    setCurrentExerciseById,
    getCachedExerciseRecommendation,
  } = useWorkoutStore()
  const { mapRIRValueToNumber, saveRIR } = useRIR()

  // Get unit from localStorage (backend uses 'massunit')
  const unit =
    typeof window !== 'undefined'
      ? ((localStorage.getItem('massunit') || 'kg') as 'kg' | 'lbs')
      : 'kg'

  const [isSaving, setIsSaving] = useState(false)
  const [saveError, setSaveError] = useState<string | null>(null)
  const [showRIRPicker, setShowRIRPicker] = useState(false)
  const [showComplete, setShowComplete] = useState(false)
  const [showExerciseComplete, setShowExerciseComplete] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [showSetSaved, setShowSetSaved] = useState(false)
  const [recommendation, setRecommendation] =
    useState<RecommendationModel | null>(null)

  // Handle deep linking - find exercise by ID if provided
  useEffect(() => {
    if (exerciseId && exercises.length > 0) {
      const exerciseIndex = exercises.findIndex((ex) => ex.Id === exerciseId)
      if (exerciseIndex === -1) {
        // Invalid exercise ID, redirect to workout
        router.replace('/workout')
      } else {
        // Sync the store's currentExerciseIndex with the URL parameter
        setCurrentExerciseById(exerciseId)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exerciseId]) // Only depend on exerciseId to prevent infinite re-renders

  // Current exercise and set info
  const currentExercise = exercises[currentExerciseIndex]
  const isWarmup = currentSetIndex < (recommendation?.WarmupsCount || 0)
  const isFirstWorkSet = currentSetIndex === (recommendation?.WarmupsCount || 0)

  // Calculate total sets including rest-pause mini-sets
  const isRestPause =
    recommendation &&
    !recommendation.IsNormalSets &&
    !recommendation.IsPyramid &&
    !recommendation.IsReversePyramid &&
    recommendation.NbPauses > 0

  const workSetCount = recommendation?.Series || 4
  const restPauseSets = isRestPause ? recommendation?.NbPauses || 0 : 0
  const totalWorkSets = workSetCount + restPauseSets
  const totalSets = (recommendation?.WarmupsCount || 0) + totalWorkSets

  const isLastSet = currentSetIndex === totalSets - 1
  const isLastExercise = currentExerciseIndex === exercises.length - 1

  // ------------------------------------------------------------
  // Build completedSets – DROP server-returned warm-ups.
  // ------------------------------------------------------------

  const rawSets =
    workoutSession?.exercises.find(
      (ex) => ex.exerciseId === currentExercise?.Id
    )?.sets || []

  // Ignore API warm-up rows that have 0 weight; we re-generate warm-ups locally
  const filteredSets = rawSets.filter(
    (s) => !(s.isWarmup && s.weight && s.weight.Kg === 0 && s.weight.Lb === 0)
  )

  // Track warm-up index so we can align with WarmUpsList for weight fallback
  let warmupSeq = 0

  const completedSets = filteredSets.map((set) => {
    const isWarmupSet = set.isWarmup

    // Weight is already stored as MultiUnityWeight object
    const weightObj = set.weight || { Kg: 0, Lb: 0 }

    // If backend dropped weight for a saved warm-up, copy from recommendation
    if (
      isWarmupSet &&
      weightObj.Kg === 0 &&
      weightObj.Lb === 0 &&
      recommendation?.WarmUpsList &&
      recommendation.WarmUpsList[warmupSeq]
    ) {
      const recWeight = recommendation.WarmUpsList[warmupSeq]!.WarmUpWeightSet
      if (recWeight) {
        weightObj.Kg = recWeight.Kg
        weightObj.Lb = recWeight.Lb
      }
    }

    if (isWarmupSet) warmupSeq++

    return {
      Id: 0,
      ExerciseId: currentExercise?.Id || -1,
      // Warm-ups: keep reps in WarmUpReps, weight in both fields
      Reps: isWarmupSet ? 0 : set.reps,
      Weight: weightObj,
      WarmUpReps: isWarmupSet ? set.reps : undefined,
      WarmUpWeightSet: isWarmupSet ? weightObj : undefined,
      RIR: set.rir,
      IsWarmups: isWarmupSet,
      SetNo: set.setNumber.toString(),
      IsFinished: true,
      IsNext: false,
    }
  })

  // Load recommendation when exercise changes
  useEffect(() => {
    if (currentExercise) {
      // First check cache
      const cached = getCachedExerciseRecommendation(currentExercise.Id)
      if (cached) {
        setRecommendation(cached)
      } else {
        // Load from API with proper error handling
        getRecommendation(currentExercise.Id)
          .then((rec) => {
            if (rec) {
              setRecommendation(rec)
            }
          })
          .catch((error) => {
            // Only log in development
            if (process.env.NODE_ENV === 'development') {
              console.error(
                '[useSetScreenLogic] Failed to load recommendation:',
                error
              )
            }
          })
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentExercise?.Id]) // Only depend on the exercise ID to prevent infinite re-renders

  // Set form data - don't default to 100 pounds for bodyweight exercises
  const [setData, setSetData] = useState({
    reps: recommendation?.Reps || 8,
    weight: recommendation?.Weight
      ? roundWeight(
          unit === 'kg' ? recommendation.Weight.Kg : recommendation.Weight.Lb
        )
      : 0,
    duration: currentExercise?.Timer || 45,
  })

  /**
   * Track which set index the current `setData` belongs to.
   * This prevents a race where the active set changes and the inputs
   * temporarily show values from a different set.
   */
  const [setDataIndex, setSetDataIndex] = useState<number>(currentSetIndex)

  // Update form data when recommendation changes
  useEffect(() => {
    if (recommendation) {
      setSetData({
        reps: recommendation.Reps || 8,
        weight: recommendation.Weight
          ? roundWeight(
              unit === 'kg'
                ? recommendation.Weight.Kg
                : recommendation.Weight.Lb
            )
          : 0,
        duration: currentExercise?.Timer || 45,
      })
      // Keep setDataIndex in sync with the currently selected set
      setSetDataIndex(currentSetIndex)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [recommendation, currentExercise?.Timer, unit]) // Only depend on specific properties to prevent infinite re-renders

  // Update form data when currentSetIndex changes
  useEffect(() => {
    if (recommendation && currentExercise) {
      const warmupCount = recommendation.WarmupsCount || 0
      const isWarmupSet = currentSetIndex < warmupCount

      if (isWarmupSet) {
        // For warmup sets, use the appropriate warmup data
        const warmupIndex = currentSetIndex
        const warmupSet = recommendation.WarmUpsList?.[warmupIndex]

        let warmupReps: number
        let warmupWeight: number

        if (warmupIndex === 0) {
          warmupReps = warmupSet?.WarmUpReps || recommendation.WarmUpReps1 || 5
          const warmupWeightSet =
            warmupSet?.WarmUpWeightSet || recommendation.WarmUpWeightSet1
          if (warmupWeightSet) {
            warmupWeight =
              unit === 'kg' ? warmupWeightSet.Kg : warmupWeightSet.Lb
          } else {
            warmupWeight = 0
          }
        } else if (warmupIndex === 1) {
          warmupReps = warmupSet?.WarmUpReps || recommendation.WarmUpReps2 || 3
          const warmupWeightSet =
            warmupSet?.WarmUpWeightSet || recommendation.WarmUpWeightSet2
          if (warmupWeightSet) {
            warmupWeight =
              unit === 'kg' ? warmupWeightSet.Kg : warmupWeightSet.Lb
          } else {
            warmupWeight = 0
          }
        } else {
          // For additional warmup sets beyond the first two
          warmupReps = warmupSet?.WarmUpReps || 5
          if (warmupSet?.WarmUpWeightSet) {
            warmupWeight =
              unit === 'kg'
                ? warmupSet.WarmUpWeightSet.Kg
                : warmupSet.WarmUpWeightSet.Lb
          } else {
            warmupWeight = 0
          }
        }

        setSetData({
          reps: warmupReps,
          weight: roundWeight(warmupWeight),
          duration: currentExercise.Timer || 45,
        })
      } else {
        // For work sets, use the main recommendation values
        setSetData({
          reps: recommendation.Reps || 8,
          weight: recommendation.Weight
            ? roundWeight(
                unit === 'kg'
                  ? recommendation.Weight.Kg
                  : recommendation.Weight.Lb
              )
            : 0,
          duration: currentExercise.Timer || 45,
        })
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentSetIndex, recommendation, currentExercise, unit]) // Watch currentSetIndex changes

  // Calculate performance percentage
  const performancePercentage = useCallback(() => {
    if (!recommendation?.ReferenceSetHistory) return null
    const currentWeight = recommendation.Weight.Lb
    const previousWeight = recommendation.ReferenceSetHistory.Weight.Lb
    return calculatePercentageChange(currentWeight, previousWeight)
  }, [recommendation])

  // Handle progression after set save
  const handleProgressAfterSave = () => {
    if (isLastSet) {
      // Show exercise complete message
      setShowExerciseComplete(true)
      // After 2 seconds, move to next exercise or complete workout
      setTimeout(() => {
        if (isLastExercise) {
          setShowComplete(true)
        } else {
          nextExercise()
          setShowExerciseComplete(false)
          // Navigate to rest timer between exercises
          router.push('/workout/rest-timer')
        }
      }, 2000)
    } else {
      // Navigate to rest timer immediately for smoother experience
      router.push('/workout/rest-timer?between-sets=true')

      // Update state in background after navigation starts
      setShowSetSaved(true)
      setIsTransitioning(true)

      // Move to next set after a minimal delay to ensure navigation has started
      setTimeout(() => {
        nextSet()
      }, 50)
    }
  }

  // Handle set save
  const handleSaveSet = async () => {
    if (!currentExercise || !workoutSession) return

    setIsSaving(true)
    setSaveError(null)

    try {
      await saveSet({
        exerciseId: currentExercise.Id,
        reps: currentExercise.IsTimeBased ? undefined : setData.reps,
        weight: setData.weight,
        isWarmup,
        setNumber: currentSetIndex + 1,
        duration: currentExercise.IsTimeBased ? setData.duration : undefined,
        RIR: undefined, // RIR will be collected after first work set
      })

      // Check if we need to show RIR picker
      if (isFirstWorkSet && !currentExercise.IsTimeBased) {
        setShowRIRPicker(true)
      } else {
        // Progress to next set or exercise
        handleProgressAfterSave()
      }
    } catch (err: unknown) {
      setSaveError(err instanceof Error ? err.message : 'Failed to save set')

      // Preserve workout data on 401 errors
      if (err && typeof err === 'object' && 'response' in err) {
        const axiosError = err as { response?: { status?: number } }
        if (axiosError.response?.status === 401 && workoutSession) {
          const preservedData = {
            exerciseId: currentExercise.Id,
            setNumber: currentSetIndex + 1,
            weight: setData.weight,
            reps: setData.reps,
            timestamp: Date.now(),
            workoutSession,
          }
          localStorage.setItem(
            'preserved_workout',
            JSON.stringify(preservedData)
          )
        }
      }
    } finally {
      setIsSaving(false)
    }
  }

  // Handle RIR selection
  const handleRIRSelect = async (rirValue: string) => {
    try {
      const numericRIR = mapRIRValueToNumber(rirValue)

      // Navigate immediately to timer with RIR value
      // This prevents the exercise page from showing after RIR selection
      router.push(
        `/workout/rest-timer?between-sets=true&rir=${numericRIR}&shouldProgress=true`
      )

      // Close modal after navigation starts
      setShowRIRPicker(false)

      // Update the current set data with RIR
      const { updateCurrentSet } = useWorkoutStore.getState()
      updateCurrentSet({ rir: numericRIR })

      // Mark RIR as captured for this exercise
      await saveRIR()
    } catch (err) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to save RIR:', err)
      }
      setShowRIRPicker(false)
      handleProgressAfterSave()
    }
  }

  // Handle RIR cancel
  const handleRIRCancel = () => {
    setShowRIRPicker(false)
    handleProgressAfterSave()
  }

  // Handle set click (for multi-set display)
  const handleSetClick = (setIndex: number) => {
    // Update the current set index to the clicked set
    setCurrentSetIndex(setIndex)

    // Update form data based on the selected set
    if (recommendation) {
      const warmupCount = recommendation.WarmupsCount || 0
      const isWarmupSet = setIndex < warmupCount

      if (isWarmupSet) {
        // For warmup sets
        const warmupIndex = setIndex
        const warmupSet = recommendation.WarmUpsList?.[warmupIndex]
        setSetData({
          reps: warmupSet?.WarmUpReps || recommendation.WarmUpReps1 || 5,
          weight:
            warmupSet?.WarmUpWeightSet?.Lb ||
            recommendation.WarmUpWeightSet1?.Lb ||
            0,
          duration: currentExercise?.Timer || 45,
        })
      } else {
        // For work sets
        setSetData({
          reps: recommendation.Reps || 8,
          weight: recommendation.Weight?.Lb || 0,
          duration: currentExercise?.Timer || 45,
        })
      }
    }
  }

  // Refetch recommendation
  const refetchRecommendation = useCallback(async () => {
    if (currentExercise) {
      const fresh = await getRecommendation(currentExercise.Id)
      setRecommendation(fresh)
    }
  }, [currentExercise, getRecommendation])

  return {
    // State
    currentExercise,
    exercises,
    currentExerciseIndex,
    isWarmup,
    isFirstWorkSet,
    totalSets,
    isLastSet,
    isLastExercise,
    currentSetIndex,
    setData,
    isSaving,
    saveError,
    showRIRPicker,
    showComplete,
    showExerciseComplete,
    isTransitioning,
    showSetSaved,
    recommendation,
    isLoading,
    error,
    completedSets,

    // Actions
    setSetData,
    setSaveError,
    handleSaveSet,
    handleRIRSelect,
    handleRIRCancel,
    refetchRecommendation,
    performancePercentage,
    handleSetClick,

    // Debug / consumers
    setDataIndex,
  }
}
