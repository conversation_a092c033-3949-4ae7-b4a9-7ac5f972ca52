import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import WorkoutPage from '@/app/workout/page'

// Mock the components
vi.mock('@/components/workout/WorkoutOverview', () => ({
  WorkoutOverview: () => <div data-testid="workout-overview">Workout Overview</div>,
}))

describe('WorkoutPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render WorkoutOverview component', () => {
    render(<WorkoutPage />)

    // Check that WorkoutOverview is rendered
    expect(screen.getByTestId('workout-overview')).toBeInTheDocument()
  })
})