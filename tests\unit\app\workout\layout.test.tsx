import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import WorkoutLayout from '@/app/workout/layout'

import { AuthGuard } from '@/components/AuthGuard'
import { useAuthStore } from '@/stores/authStore'

// Mock the AuthGuard component
vi.mock('@/components/AuthGuard', () => ({
  AuthGuard: vi.fn(({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-guard">{children}</div>
  )),
}))

// Mock the auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    isAuthenticated: true,
    isLoading: false,
    hasHydrated: true,
  })),
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
  }),
  usePathname: () => '/workout',
}))

describe('WorkoutLayout', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should wrap children in AuthGuard', () => {
    const TestChild = () => <div data-testid="test-child">Test Content</div>

    render(
      <WorkoutLayout>
        <TestChild />
      </WorkoutLayout>
    )

    // Check that AuthGuard is present
    expect(screen.getByTestId('auth-guard')).toBeInTheDocument()

    // Check that children are rendered inside AuthGuard
    const authGuard = screen.getByTestId('auth-guard')
    const testChild = screen.getByTestId('test-child')
    
    expect(authGuard).toContainElement(testChild)
  })

  it('should protect all workout routes with authentication', () => {
    // Mock unauthenticated state
    const mockUseAuthStore = vi.fn(() => ({
      isAuthenticated: false,
      isLoading: false,
      hasHydrated: true,
    }))
    
    vi.mocked(useAuthStore).mockImplementation(mockUseAuthStore)

    // Re-mock AuthGuard to simulate redirect behavior
    vi.mocked(AuthGuard).mockImplementation(
      ({ children }: { children: React.ReactNode }) => {
        const { isAuthenticated } = mockUseAuthStore()
        if (!isAuthenticated) {
          return null // AuthGuard would redirect, so nothing renders
        }
        return <div data-testid="auth-guard">{children}</div>
      }
    )

    const TestChild = () => <div data-testid="test-child">Test Content</div>

    render(
      <WorkoutLayout>
        <TestChild />
      </WorkoutLayout>
    )

    // Children should not be rendered when not authenticated
    expect(screen.queryByTestId('test-child')).not.toBeInTheDocument()
  })
})